---
title: ClickUp
subtitle: Learn how to use ClickUp with Composio
---

## Overview

### Enum

`CLIC<PERSON><PERSON>`

### Description

ClickUp unifies tasks, docs, goals, and chat in a single platform, allowing teams to plan, organize, and collaborate across projects with customizable workflows

### Authentication Details

<Accordion title="OAUTH2">
<ParamField path="client_id" type="string" required={true}>
</ParamField>

<ParamField path="client_secret" type="string" required={true}>
</ParamField>

<ParamField path="oauth_redirect_uri" type="string" default="https://backend.composio.dev/api/v1/auth-apps/add">
</ParamField>

<ParamField path="scopes" type="string">
</ParamField>

</Accordion>

<Accordion title="API_KEY">
<ParamField path="api_key" type="string" required={true}>
</ParamField>

</Accordion>

## Actions

<AccordionGroup>
<Accordion title="CLICKUP_CREATE_TASK">
Create a new task in ClickUp with specified details including name, description, assignees, and due date.

**Action Parameters**

<ParamField path="name" type="string" required={true}>
Task name or title
</ParamField>

<ParamField path="description" type="string">
Task description or content
</ParamField>

<ParamField path="list_id" type="string" required={true}>
ID of the list where the task will be created
</ParamField>

<ParamField path="assignees" type="array">
Array of user IDs to assign to the task
</ParamField>

<ParamField path="priority" type="integer">
Priority level (1=Urgent, 2=High, 3=Normal, 4=Low)
</ParamField>

<ParamField path="due_date" type="string">
Due date in Unix timestamp format
</ParamField>

<ParamField path="status" type="string">
Task status (e.g., "to do", "in progress", "complete")
</ParamField>

<ParamField path="tags" type="array">
Array of tag names to apply to the task
</ParamField>

**Action Response**

<ParamField path="data" type="object">
Created task object with ID and details
</ParamField>

<ParamField path="error" type="">
Error message if creation failed
</ParamField>

<ParamField path="successful" type="boolean">
Whether the task was created successfully
</ParamField>

</Accordion>

<Accordion title="CLICKUP_GET_TASKS">
Retrieve tasks from a specified ClickUp list with optional filtering and pagination.

**Action Parameters**

<ParamField path="list_id" type="string" required={true}>
ID of the list to retrieve tasks from
</ParamField>

<ParamField path="archived" type="boolean">
Include archived tasks in results
</ParamField>

<ParamField path="page" type="integer">
Page number for pagination (0-based)
</ParamField>

<ParamField path="order_by" type="string">
Field to order results by (e.g., "created", "updated", "due_date")
</ParamField>

<ParamField path="reverse" type="boolean">
Reverse the order of results
</ParamField>

<ParamField path="subtasks" type="boolean">
Include subtasks in results
</ParamField>

<ParamField path="statuses" type="array">
Array of status names to filter by
</ParamField>

<ParamField path="include_closed" type="boolean">
Include closed/completed tasks
</ParamField>

**Action Response**

<ParamField path="data" type="object">
Object containing tasks array and pagination info
</ParamField>

<ParamField path="error" type="">
Error message if retrieval failed
</ParamField>

<ParamField path="successful" type="boolean">
Whether the tasks were retrieved successfully
</ParamField>

</Accordion>

<Accordion title="CLICKUP_UPDATE_TASK">
Update an existing task in ClickUp with new information.

**Action Parameters**

<ParamField path="task_id" type="string" required={true}>
ID of the task to update
</ParamField>

<ParamField path="name" type="string">
New task name
</ParamField>

<ParamField path="description" type="string">
New task description
</ParamField>

<ParamField path="status" type="string">
New task status
</ParamField>

<ParamField path="priority" type="integer">
New priority level (1-4)
</ParamField>

<ParamField path="due_date" type="string">
New due date in Unix timestamp format
</ParamField>

<ParamField path="assignees" type="array">
New array of user IDs to assign
</ParamField>

**Action Response**

<ParamField path="data" type="object">
Updated task object
</ParamField>

<ParamField path="error" type="">
Error message if update failed
</ParamField>

<ParamField path="successful" type="boolean">
Whether the task was updated successfully
</ParamField>

</Accordion>

<Accordion title="CLICKUP_GET_LISTS">
Get all lists from a specified ClickUp space.

**Action Parameters**

<ParamField path="space_id" type="string" required={true}>
ID of the space to get lists from
</ParamField>

<ParamField path="archived" type="boolean">
Include archived lists
</ParamField>

**Action Response**

<ParamField path="data" type="object">
Object containing lists array
</ParamField>

<ParamField path="error" type="">
Error message if retrieval failed
</ParamField>

<ParamField path="successful" type="boolean">
Whether the lists were retrieved successfully
</ParamField>

</Accordion>

<Accordion title="CLICKUP_GET_SPACES">
Get all spaces from a specified ClickUp team/workspace.

**Action Parameters**

<ParamField path="team_id" type="string" required={true}>
ID of the team to get spaces from
</ParamField>

<ParamField path="archived" type="boolean">
Include archived spaces
</ParamField>

**Action Response**

<ParamField path="data" type="object">
Object containing spaces array
</ParamField>

<ParamField path="error" type="">
Error message if retrieval failed
</ParamField>

<ParamField path="successful" type="boolean">
Whether the spaces were retrieved successfully
</ParamField>

</Accordion>

<Accordion title="CLICKUP_GET_TEAMS">
Get all teams (workspaces) for the authenticated user.

**Action Parameters**

No parameters required.

**Action Response**

<ParamField path="data" type="object">
Object containing teams array
</ParamField>

<ParamField path="error" type="">
Error message if retrieval failed
</ParamField>

<ParamField path="successful" type="boolean">
Whether the teams were retrieved successfully
</ParamField>

</Accordion>

</AccordionGroup>
