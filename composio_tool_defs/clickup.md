# ClickUp Action Mappings Summary

## Overview

ClickUp is a productivity platform that unifies tasks, docs, goals, and chat in a single platform, allowing teams to plan, organize, and collaborate across projects with customizable workflows. This document summarizes the available action mappings for ClickUp integration with Composio.

## Authentication Methods

ClickUp supports two authentication methods:

1. **OAuth2** - Requires client_id, client_secret, and supports various scopes including task management, team management, workspace management, and more.

2. **API Key** - Simpler authentication requiring just an API key.

## Action Mappings

### Task Management

**CLICKUP_CREATE_TASK** - Creates a new task in a specified list. Required parameters include list_id and name. Optional parameters include assignees, description, due dates, priority, tags, and more. This action is fundamental for task creation within the ClickUp ecosystem.

**CLICKUP_DELETE_TASK** - Removes a task from the workspace. Requires task_id as a parameter, with an optional team_id parameter.

**CLICKUP_ADD_TASK_LINK** - Links two tasks together, requiring both the task_id and links_to parameters to establish the connection.

**CLICKUP_DELETE_TASK_LINK** - Removes the link between two tasks, requiring the same parameters as the add link action.

**CLICKUP_ADD_TASK_TO_LIST** - Adds an existing task to an additional list. This requires the Tasks in Multiple Lists ClickApp to be enabled and needs both task_id and list_id parameters.

**CLICKUP_CREATE_TASK_FROM_TEMPLATE** - Creates a new task using a predefined task template, requiring list_id, name, and template_id parameters.

**CLICKUP_ADD_DEPENDENCY** - Sets a task as waiting on or blocking another task, establishing dependency relationships between tasks.

**CLICKUP_DELETE_DEPENDENCY** - Removes dependency relationships between tasks, requiring dependency_of, depends_on, and task_id parameters.

### Comments and Attachments

**CLICKUP_CREATE_TASK_COMMENT** - Adds a new comment to a task. Required parameters include task_id, comment_text, assignee, and notify_all.

**CLICKUP_DELETE_COMMENT** - Deletes a task comment, requiring only the comment_id parameter.

**CLICKUP_CREATE_LIST_COMMENT** - Adds a comment to a list, requiring list_id, comment_text, assignee, and notify_all parameters.

**CLICKUP_CREATE_CHAT_VIEW_COMMENT** - Adds a new comment to a chat view, requiring view_id, comment_text, and notify_all parameters.

**CLICKUP_CREATE_TASK_ATTACHMENT** - Uploads a file to a task as an attachment. This action uses multipart/form-data as the content type and requires task_id parameter.

**CLICKUP_ATTACHMENTS_UPLOAD_FILE_TO_TASK_AS_ATTACHMENT** - A deprecated version of the attachment upload functionality, replaced by CREATE_TASK_ATTACHMENT.

### Checklists

**CLICKUP_CREATE_CHECKLIST** - Adds a new checklist to a task, requiring task_id and name parameters.

**CLICKUP_DELETE_CHECKLIST** - Deletes a checklist from a task, requiring only the checklist_id parameter.

**CLICKUP_EDIT_CHECKLIST** - Renames a task checklist or reorders it to appear above or below other checklists on a task.

**CLICKUP_CREATE_CHECKLIST_ITEM** - Adds a line item to a task checklist, requiring checklist_id parameter.

**CLICKUP_DELETE_CHECKLIST_ITEM** - Deletes a line item from a task checklist, requiring both checklist_id and checklist_item_id parameters.

### Lists and Folders

**CLICKUP_CREATE_LIST** - Adds a new list to a folder, requiring folder_id and name parameters.

**CLICKUP_DELETE_LIST** - Deletes a list from the workspace, requiring only the list_id parameter.

**CLICKUP_CREATE_FOLDERLESS_LIST** - Adds a new list directly in a space without a folder, requiring space_id and name parameters.

**CLICKUP_CREATE_FOLDER** - Adds a new folder to a space, requiring space_id and name parameters.

**CLICKUP_DELETE_FOLDER** - Deletes a folder from the workspace, requiring only the folder_id parameter.

### Spaces

**CLICKUP_CREATE_SPACE** - Adds a new space to a workspace, requiring team_id, name, and multiple_assignees parameters.

**CLICKUP_DELETE_SPACE** - Deletes a space from the workspace, requiring only the space_id parameter.

**CLICKUP_CREATE_SPACE_TAG** - Adds a new task tag to a space, requiring space_id parameter.

**CLICKUP_DELETE_SPACE_TAG** - Deletes a task tag from a space, requiring space_id and tag_name parameters.

### Views

**CLICKUP_CREATE_LIST_VIEW** - Adds a view (list, board, calendar, etc.) to a list, requiring list_id, name, and type parameters.

**CLICKUP_CREATE_FOLDER_VIEW** - Adds a view to a folder, requiring folder_id, name, and type parameters.

**CLICKUP_CREATE_SPACE_VIEW** - Adds a view to a space, requiring space_id, name, and type parameters.

**CLICKUP_CREATE_WORKSPACE_EVERYTHING_LEVEL_VIEW** - Adds a view at the everything level of a workspace, requiring team_id, name, and type parameters.

**CLICKUP_DELETE_VIEW** - Deletes a specific view by its ID, requiring the view_id parameter.

### Goals and Key Results

**CLICKUP_CREATE_GOAL** - Adds a new goal to a workspace, requiring team_id, name, description, due_date, multiple_owners, owners, and color parameters.

**CLICKUP_DELETE_GOAL** - Removes a goal from the workspace, requiring only the goal_id parameter.

**CLICKUP_CREATE_KEY_RESULT** - Adds a target to a goal, requiring goal_id, name, owners, type, unit, steps_start, steps_end, list_ids, and task_ids parameters.

**CLICKUP_DELETE_KEY_RESULT** - Deletes a target from a goal, requiring only the key_result_id parameter.

### Time Tracking

**CLICKUP_CREATE_A_TIME_ENTRY** - Creates a time entry, requiring team_Id, start, and duration parameters.

**CLICKUP_DELETE_A_TIME_ENTRY** - Deletes a time entry from a workspace, requiring team_id and timer_id parameters.

**CLICKUP_ADD_TAGS_FROM_TIME_ENTRIES** - Adds a label to a time entry, requiring team_id, time_entry_ids, and tags parameters.

**CLICKUP_CHANGE_TAG_NAMES_FROM_TIME_ENTRIES** - Renames a time entry label, requiring team_id, name, new_name, tag_bg, and tag_fg parameters.

**CLICKUP_DELETE_TIME_TRACKED** - A legacy time tracking endpoint for deleting time tracked, requiring task_id and interval_id parameters.

### Tags

**CLICKUP_ADD_TAG_TO_TASK** - Adds a tag to a task, requiring task_id and tag_name parameters.

### Guest Management

**CLICKUP_ADD_GUEST_TO_FOLDER** - Shares a folder with a guest, requiring folder_id, guest_id, and permission_level parameters. Only available to workspaces on the enterprise plan.

**CLICKUP_ADD_GUEST_TO_LIST** - Shares a list with a guest, requiring list_id, guest_id, and permission_level parameters. Only available to workspaces on the enterprise plan.

**CLICKUP_ADD_GUEST_TO_TASK** - Shares a task with a guest, requiring task_id, guest_id, and permission_level parameters. Only available to workspaces on the enterprise plan.

### Team Management

**CLICKUP_CREATE_TEAM** - Creates teams within workspaces, requiring team_id, name, and members parameters.

**CLICKUP_DELETE_TEAM** - Removes a user group from a workspace, requiring the group_id parameter.

### Webhooks

**CLICKUP_CREATE_WEBHOOK** - Sets up a webhook to monitor for events, requiring team_id, endpoint, and events parameters.

**CLICKUP_DELETE_WEBHOOK** - Deletes a webhook to stop monitoring events, requiring only the webhook_id parameter.

### Authorization

**CLICKUP_AUTHORIZATION_GET_ACCESS_TOKEN** - A deprecated endpoint for obtaining access tokens, replaced by get_access_token.

**CLICKUP_AUTHORIZATION_GET_WORK_SPACE_LIST** - A deprecated endpoint for viewing available workspaces, replaced by get_authorized_teams_workspaces.

**CLICKUP_AUTHORIZATION_VIEW_ACCOUNT_DETAILS** - A deprecated endpoint for viewing authenticated user details, replaced by get_authorized_user.

## Response Structure

All ClickUp actions return a consistent response structure with three main fields:

1. **data** - An object containing the response data specific to the action
2. **error** - Contains error information if the action fails
3. **successful** - A boolean indicating whether the action was successful

## Special Notes

1. Several actions have been deprecated and replaced with newer versions.
2. Some features, like guest management, are only available on enterprise plans.
3. The API supports custom task IDs through the custom_task_ids parameter in many actions.
4. Time tracking has both modern and legacy endpoints, with the recommendation to use the newer time tracking API endpoints.
