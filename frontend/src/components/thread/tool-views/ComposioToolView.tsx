import React, { useState } from 'react';
import { ToolViewProps } from './types';
import { formatTimestamp, getToolTitle } from './utils';
import {
  CircleDashed,
  CheckCircle,
  AlertTriangle,
  Mail,
  FileText,
  Github,
  Slack,
  Calendar,
  Database,
  HardDrive,
  MessageSquare,
  Settings,
  Activity,
} from 'lucide-react';
import {
  SiGmail,
  SiNotion,
  SiGithub,
  SiSlack,
  SiGoogledrive,
  SiDiscord,
  SiLinear,
  SiGoogle,
  SiClickup,
} from 'react-icons/si';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { GenericToolView } from './GenericToolView';

// Service-specific icons with better coverage
const getServiceIcon = (serviceName: string) => {
  const service = serviceName.toLowerCase();
  switch (service) {
    case 'gmail':
    case 'google_mail':
    case 'googlemail':
      return SiGmail;
    case 'notion':
      return SiNotion;
    case 'github':
      return SiGithub;
    case 'slack':
      return SiSlack;
    case 'googledrive':
    case 'google_drive':
    case 'google-drive':
    case 'drive':
      return SiGoogledrive;
    case 'google_calendar':
    case 'googlecalendar':
    case 'calendar':
      return SiGoogle;
    case 'discord':
      return SiDiscord;
    case 'linear':
      return SiLinear;
    case 'clickup':
    case 'click_up':
    case 'click-up':
      return SiClickup;
    default:
      return Database;
  }
};

// Service-specific colors
const getServiceColor = (serviceName: string) => {
  const service = serviceName.toLowerCase();
  switch (service) {
    case 'gmail':
    case 'google_mail':
    case 'googlemail':
      return 'text-red-600 dark:text-red-400';
    case 'notion':
      return 'text-gray-800 dark:text-gray-200';
    case 'github':
      return 'text-gray-900 dark:text-gray-100';
    case 'slack':
      return 'text-purple-600 dark:text-purple-400';
    case 'googledrive':
    case 'google_drive':
    case 'google-drive':
    case 'drive':
      return 'text-blue-600 dark:text-blue-400';
    case 'google_calendar':
    case 'googlecalendar':
    case 'calendar':
      return 'text-green-600 dark:text-green-400';
    case 'discord':
      return 'text-indigo-600 dark:text-indigo-400';
    case 'linear':
      return 'text-purple-600 dark:text-purple-400';
    case 'clickup':
    case 'click_up':
    case 'click-up':
      return 'text-pink-600 dark:text-pink-400';
    default:
      return 'text-gray-600 dark:text-gray-400';
  }
};

// Better action name formatting
const formatActionName = (action: string) => {
  return action
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Enhanced response parsing with intelligent structure detection
const parseToolResponse = (toolContent: string, fallbackSuccess: boolean) => {
  if (!toolContent || toolContent === 'STREAMING') {
    return null;
  }

  try {
    // Try to parse as JSON first
    const parsed = JSON.parse(toolContent);

    // Handle new structured response format
    if (parsed.success !== undefined) {
      return {
        success: parsed.success,
        data: parsed.data || parsed.result || parsed.content,
        error: parsed.error || parsed.message,
        service: parsed.service,
        action: parsed.action,
        metadata: parsed.metadata,
        rawData: parsed
      };
    }

    // Handle legacy tool_result format
    if (parsed.content) {
      const toolResultMatch = parsed.content.match(
        /<tool_result>\s*<([^>]+)>([\s\S]*?)<\/[^>]+>\s*<\/tool_result>/
      );

      if (toolResultMatch) {
        const resultContent = toolResultMatch[2].trim();

        // Better error detection
        const hasError = resultContent.includes('❌') ||
                        resultContent.includes('Error:') ||
                        resultContent.includes('Failed:') ||
                        resultContent.toLowerCase().includes('authentication failed') ||
                        resultContent.toLowerCase().includes('permission denied') ||
                        resultContent.toLowerCase().includes('rate limit');

        return {
          success: !hasError,
          data: resultContent,
          error: hasError ? resultContent : null,
          rawData: parsed
        };
      }
    }

    // Handle direct response objects
    if (typeof parsed === 'object') {
      // Check for common error indicators in the object
      const hasError = 'error' in parsed ||
                      'error_message' in parsed ||
                      ('status' in parsed && parsed.status === 'error') ||
                      ('success' in parsed && parsed.success === false);

      return {
        success: !hasError,
        data: hasError ? null : parsed,
        error: hasError ? (parsed.error || parsed.error_message || 'Operation failed') : null,
        rawData: parsed
      };
    }

    // Fallback for other JSON content
    return {
      success: fallbackSuccess,
      data: parsed,
      error: null,
      rawData: parsed
    };

  } catch (e) {
    // Handle plain text responses
    const content = toolContent.trim();

    // Better plain text error detection
    const hasError = content.includes('❌') ||
                    content.includes('Error:') ||
                    content.includes('Failed:') ||
                    content.toLowerCase().includes('error') ||
                    content.toLowerCase().includes('failed') ||
                    content.toLowerCase().includes('authentication failed') ||
                    content.toLowerCase().includes('permission denied');

    return {
      success: !hasError,
      data: hasError ? null : content,
      error: hasError ? content : null,
      rawData: content
    };
  }
};

// Parse request details from assistant content
const parseRequestDetails = (assistantContent: string) => {
  if (!assistantContent) return null;

  try {
    const parsed = JSON.parse(assistantContent);
    const content = parsed.content || assistantContent;

    // Extract XML content
    const xmlMatch = content.match(/<([^>]+)([^>]*)>([\s\S]*?)<\/[^>]+>/);
    if (xmlMatch) {
      const tagName = xmlMatch[1];
      const attributes = xmlMatch[2];
      const bodyContent = xmlMatch[3];

      // Parse attributes
      const attrs: Record<string, string> = {};
      const attrMatches = attributes.matchAll(/(\w+)="([^"]*)"/g);
      for (const match of attrMatches) {
        attrs[match[1]] = match[2];
      }

      // Extract service and action from tag name or attributes
      const serviceName = tagName.split('-')[0] || 'unknown';
      const actionName = attrs.action || tagName.split('-').slice(1).join('_') || 'action';

      return {
        service: serviceName,
        action: actionName,
        parameters: Object.fromEntries(
          Object.entries(attrs).filter(([key]) => key !== 'action')
        ),
        content: bodyContent?.trim() || null,
        tagName
      };
    }

    return null;
  } catch (e) {
    return null;
  }
};

// Smart data renderer for structured display
const renderStructuredData = (data: any): React.ReactNode => {
  if (!data) return null;

  if (typeof data === 'string') {
    // Check if it's a URL
    if (data.startsWith('http://') || data.startsWith('https://')) {
      return (
        <a
          href={data}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 dark:text-blue-400 hover:underline break-all"
        >
          {data}
        </a>
      );
    }

    // Check if it looks like an email
    if (data.includes('@') && data.includes('.')) {
      return (
        <a
          href={`mailto:${data}`}
          className="text-blue-600 dark:text-blue-400 hover:underline"
        >
          {data}
        </a>
      );
    }

    return <span className="break-words">{data}</span>;
  }

  if (Array.isArray(data)) {
    return (
      <div className="space-y-2">
        {data.map((item, index) => (
          <div key={index} className="border-l-2 border-zinc-200 dark:border-zinc-700 pl-3">
            {renderStructuredData(item)}
          </div>
        ))}
      </div>
    );
  }

  if (typeof data === 'object') {
    // Handle common patterns
    if (data.id && data.title) {
      return (
        <div className="bg-zinc-50 dark:bg-zinc-900 rounded-md p-3 border border-zinc-200 dark:border-zinc-800">
          <div className="font-medium text-zinc-900 dark:text-zinc-100">{data.title}</div>
          <div className="text-xs text-zinc-500 dark:text-zinc-400 font-mono">ID: {data.id}</div>
          {data.url && (
            <a
              href={data.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs text-blue-600 dark:text-blue-400 hover:underline mt-1 inline-block"
            >
              View →
            </a>
          )}
        </div>
      );
    }

    // Handle message or email patterns
    if (data.subject || data.from || data.to) {
      return (
        <div className="bg-zinc-50 dark:bg-zinc-900 rounded-md p-3 border border-zinc-200 dark:border-zinc-800">
          {data.subject && (
            <div className="font-medium text-zinc-900 dark:text-zinc-100 mb-1">{data.subject}</div>
          )}
          <div className="space-y-1 text-xs">
            {data.from && <div><span className="text-zinc-600 dark:text-zinc-400">From:</span> {data.from}</div>}
            {data.to && <div><span className="text-zinc-600 dark:text-zinc-400">To:</span> {data.to}</div>}
            {data.date && <div><span className="text-zinc-600 dark:text-zinc-400">Date:</span> {data.date}</div>}
          </div>
          {data.snippet && (
            <div className="mt-2 text-sm text-zinc-700 dark:text-zinc-300 italic">
              "{data.snippet}"
            </div>
          )}
        </div>
      );
    }

    // Generic object display
    return (
      <div className="bg-zinc-50 dark:bg-zinc-900 rounded-md p-3 border border-zinc-200 dark:border-zinc-800">
        <div className="space-y-2">
          {Object.entries(data).map(([key, value]) => (
            <div key={key} className="flex flex-col gap-1">
              <span className="text-xs font-medium text-zinc-600 dark:text-zinc-400 uppercase tracking-wide">
                {key.replace(/_/g, ' ')}:
              </span>
              <div className="text-sm text-zinc-700 dark:text-zinc-300">
                {renderStructuredData(value)}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return <span>{String(data)}</span>;
};

// Copy to clipboard helper
const copyToClipboard = async (text: string, label: string) => {
  try {
    await navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  } catch (err) {
    toast.error('Failed to copy to clipboard');
  }
};

export function ComposioToolView({
  name = 'composio-tool',
  assistantContent,
  toolContent,
  isSuccess = true,
  isStreaming = false,
  assistantTimestamp,
  toolTimestamp,
}: ToolViewProps) {
  // Parse service name and display info
  const { serviceName, displayName } = React.useMemo(() => {
    const match = name.match(/^([^-]+)-action$/);
    if (match) {
      const service = match[1];
      return {
        serviceName: service,
        displayName: `${service.charAt(0).toUpperCase() + service.slice(1)}`
      };
    }

    const service = name.split('-')[0] || 'composio';
    return {
      serviceName: service,
      displayName: service.charAt(0).toUpperCase() + service.slice(1)
    };
  }, [name]);

  const ServiceIcon = getServiceIcon(serviceName);
  const serviceColor = getServiceColor(serviceName);

  // Parse request and response
  const requestDetails = React.useMemo(() =>
    parseRequestDetails(assistantContent || ''), [assistantContent]
  );

  const responseDetails = React.useMemo(() =>
    parseToolResponse(toolContent || '', isSuccess), [toolContent, isSuccess]
  );

  // Determine actual success state
  const actualSuccess = responseDetails?.success ?? isSuccess;

  // Fall back to generic view if we can't parse the service details
  if (!requestDetails && !responseDetails) {
    return (
      <GenericToolView
        name={name}
        assistantContent={assistantContent}
        toolContent={toolContent}
        assistantTimestamp={assistantTimestamp}
        toolTimestamp={toolTimestamp}
        isSuccess={isSuccess}
        isStreaming={isStreaming}
      />
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 p-4 overflow-auto">
        {isStreaming ? (
          // Streaming State
          <div className="border border-zinc-200 dark:border-zinc-800 rounded-md overflow-hidden shadow-sm bg-white dark:bg-zinc-950 h-full flex flex-col">
            <div className="flex items-center p-2 bg-zinc-100 dark:bg-zinc-900 text-zinc-900 dark:text-zinc-100 border-b border-zinc-200 dark:border-zinc-800">
              <ServiceIcon className={cn("h-4 w-4 mr-2", serviceColor)} />
              <span className="text-xs font-medium">{displayName}</span>
            </div>

            <div className="flex-1 flex items-center justify-center p-8 bg-white dark:bg-zinc-950">
              <div className="text-center">
                <CircleDashed className="h-8 w-8 mx-auto mb-3 text-blue-500 animate-spin" />
                <p className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                  Executing {serviceName} action...
                </p>
                <p className="text-xs mt-1 text-zinc-500 dark:text-zinc-400">
                  {requestDetails?.action ? formatActionName(requestDetails.action) : 'Processing integration'}
                </p>
              </div>
            </div>
          </div>
        ) : (
          // Completed State
          <div className="border border-zinc-200 dark:border-zinc-800 rounded-md overflow-hidden shadow-sm bg-white dark:bg-zinc-950 h-full flex flex-col">
            {/* Header */}
            <div className="flex items-center p-2 bg-zinc-100 dark:bg-zinc-900 text-zinc-900 dark:text-zinc-100 border-b border-zinc-200 dark:border-zinc-800">
              <ServiceIcon className={cn("h-4 w-4 mr-2", serviceColor)} />
              <span className="text-xs font-medium">{displayName}</span>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-auto bg-white dark:bg-zinc-950 text-zinc-900 dark:text-zinc-100">
              <div className="p-4 space-y-4">
                {/* Request Summary */}
                {requestDetails && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-zinc-900 dark:text-zinc-100">Request</h4>
                    <div className="bg-zinc-50 dark:bg-zinc-900 rounded-md p-3 border border-zinc-200 dark:border-zinc-800">
                      <div className="space-y-2 text-xs">
                        <div>
                          <span className="font-medium text-zinc-600 dark:text-zinc-400">Action:</span>
                          <span className="ml-2 font-mono text-blue-600 dark:text-blue-400">
                            {formatActionName(requestDetails.action)}
                          </span>
                        </div>
                        <div>
                          <span className="font-medium text-zinc-600 dark:text-zinc-400">Service:</span>
                          <span className="ml-2 text-zinc-700 dark:text-zinc-300">
                            {requestDetails.service}
                          </span>
                        </div>
                      </div>

                      {Object.keys(requestDetails.parameters).length > 0 && (
                        <div className="mt-3 pt-3 border-t border-zinc-200 dark:border-zinc-700">
                          <div className="text-xs font-medium text-zinc-600 dark:text-zinc-400 mb-2">Parameters:</div>
                          <div className="space-y-2">
                            {Object.entries(requestDetails.parameters).map(([key, value]) => (
                              <div key={key} className="text-xs">
                                <div className="font-medium text-zinc-600 dark:text-zinc-400 mb-1">{key}:</div>
                                <div className="text-zinc-700 dark:text-zinc-300 pl-2 break-words">
                                  {value}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {requestDetails.content && (
                        <div className="mt-3 pt-3 border-t border-zinc-200 dark:border-zinc-700">
                          <span className="text-xs font-medium text-zinc-600 dark:text-zinc-400">Content:</span>
                          <div className="mt-1 text-xs text-zinc-700 dark:text-zinc-300 font-mono bg-zinc-100 dark:bg-zinc-800 rounded p-2">
                            {requestDetails.content}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Response */}
                {responseDetails && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-zinc-900 dark:text-zinc-100">Response</h4>
                      <div className={cn(
                        "px-2 py-1 rounded-full text-xs font-medium",
                        actualSuccess
                          ? "bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300"
                          : "bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-300"
                      )}>
                        {actualSuccess ? 'Success' : 'Failed'}
                      </div>
                    </div>

                    <div className="bg-zinc-50 dark:bg-zinc-900 rounded-md border border-zinc-200 dark:border-zinc-800 overflow-hidden">
                      {responseDetails.error ? (
                        <div className="p-3">
                          <div className="flex items-start gap-2">
                            <AlertTriangle className="h-4 w-4 text-red-500 flex-shrink-0 mt-0.5" />
                            <div className="text-sm text-red-600 dark:text-red-400">
                              {responseDetails.error}
                            </div>
                          </div>
                        </div>
                      ) : responseDetails.data ? (
                        <div className="p-3">
                          <div className="text-sm">
                            {renderStructuredData(responseDetails.data)}
                          </div>
                        </div>
                      ) : (
                        <div className="p-3 text-sm text-zinc-500 dark:text-zinc-400">
                          No response data available
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-zinc-200 dark:border-zinc-800">
        <div className="flex items-center justify-between text-xs text-zinc-500 dark:text-zinc-400">
          {!isStreaming && (
            <div className="flex items-center gap-2">
              {actualSuccess ? (
                <CheckCircle className="h-3.5 w-3.5 text-emerald-500" />
              ) : (
                <AlertTriangle className="h-3.5 w-3.5 text-red-500" />
              )}
              <span>
                {actualSuccess
                  ? `${displayName} action completed successfully`
                  : `${displayName} action failed`
                }
              </span>
            </div>
          )}

          {isStreaming && (
            <div className="flex items-center gap-2">
              <CircleDashed className="h-3.5 w-3.5 text-blue-500 animate-spin" />
              <span>Executing {serviceName} action...</span>
            </div>
          )}

          <div className="text-xs">
            {toolTimestamp && !isStreaming
              ? formatTimestamp(toolTimestamp)
              : assistantTimestamp
                ? formatTimestamp(assistantTimestamp)
                : ''
            }
          </div>
        </div>
      </div>
    </div>
  );
}
