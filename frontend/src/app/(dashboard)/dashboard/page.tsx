'use client';

import React, { useState, Suspense, useEffect, useRef } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import AnimatedLoadingSkeleton from '@/components/ui/animated-loading-skeleton';
import { useRouter } from 'next/navigation';
import { Menu } from 'lucide-react';
import {
  ChatInput,
  ChatInputHandles,
} from '@/components/thread/chat-input/chat-input';
import {
  initiateAgent,
  createThread,
  addUserMessage,
  startAgent,
  createProject,
  BillingError,
} from '@/lib/api';
import { generateThreadName } from '@/lib/actions/threads';
import { useIsMobile } from '@/hooks/use-mobile';
import { useSidebar } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useBillingError } from '@/hooks/useBillingError';
import { BillingErrorAlert } from '@/components/billing/usage-limit-alert';
import { useAccounts } from '@/hooks/use-accounts';
import { isLocalMode, config } from '@/lib/config';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { useAuth } from '@/components/AuthProvider';

// Constant for localStorage key to ensure consistency
const PENDING_PROMPT_KEY = 'pendingAgentPrompt';

function DashboardContent() {
  const [inputValue, setInputValue] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [autoSubmit, setAutoSubmit] = useState(false);
  const [userFirstName, setUserFirstName] = useState<string>('');

  const { billingError, handleBillingError, clearBillingError } =
    useBillingError();
  const { user } = useAuth();
  const router = useRouter();
  const isMobile = useIsMobile();
  const { setOpenMobile } = useSidebar();
  const { data: accounts } = useAccounts();
  const personalAccount = accounts?.find((account) => account.personal_account);
  const chatInputRef = useRef<ChatInputHandles>(null);

  const secondaryGradient =
    'bg-gradient-to-r from-blue-500 to-blue-500 bg-clip-text text-transparent';

  const handleSubmit = async (
    message: string,
    options?: {
      model_name?: string;
      enable_thinking?: boolean;
      reasoning_effort?: string;
      stream?: boolean;
      enable_context_manager?: boolean;
    },
  ) => {
    if (
      (!message.trim() && !chatInputRef.current?.getPendingFiles().length) ||
      isSubmitting
    ) {
      return;
    }

    try {
      setIsSubmitting(true);
      clearBillingError();

      const files = chatInputRef.current?.getPendingFiles() || [];

      if (files.length > 0) {
        // ---- Handle file uploads (NO CHANGES NEEDED HERE) ----
        console.log(`Submitting with files: ${files.length} files`);
        const formData = new FormData();

        // Use 'prompt' key instead of 'message'
        formData.append('prompt', message);

        // Append files
        files.forEach((file, index) => {
          formData.append('files', file, file.name);
        });

        // Append model options
        if (options?.model_name)
          formData.append('model_name', options.model_name);
        if (options?.enable_thinking !== undefined)
          formData.append('enable_thinking', String(options.enable_thinking));
        if (options?.reasoning_effort)
          formData.append('reasoning_effort', options.reasoning_effort);
        if (options?.stream !== undefined)
          formData.append('stream', String(options.stream));
        formData.append(
          'enable_context_manager',
          String(options?.enable_context_manager ?? false),
        );

        console.log('FormData content:', Array.from(formData.entries()));

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (response.ok) {
          const data = await response.json();
          router.push(`/agents/${data.thread_id}`);
        }
        chatInputRef.current?.clearPendingFiles();
      } else {
        // ---- Handle text-only messages (NO CHANGES NEEDED HERE) ----
        console.log(`Submitting text-only message: "${message}"`);
        const projectName = await generateThreadName(message);
        const newProject = await createProject({
          name: projectName,
          description: '',
        });
        const thread = await createThread(newProject.id);
        await addUserMessage(thread.thread_id, message);
        await startAgent(thread.thread_id, options); // Pass original options here
        router.push(`/agents/${thread.thread_id}`);
      }
    } catch (error: any) {
      console.error('Error in handleSubmit:', error);
      if (error.response?.status === 403) {
        handleBillingError();
      } else {
        toast.error('Failed to start conversation');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Extract user's first name
  useEffect(() => {
    if (user) {
      const fullName = user.user_metadata?.name || user.email?.split('@')[0] || 'there';
      const firstName = fullName.split(' ')[0];
      setUserFirstName(firstName);
    }
  }, [user]);

  // Check for pending prompt in localStorage on mount
  useEffect(() => {
    // Use a small delay to ensure we're fully mounted
    const timer = setTimeout(() => {
      const pendingPrompt = localStorage.getItem(PENDING_PROMPT_KEY);

      if (pendingPrompt) {
        setInputValue(pendingPrompt);
        setAutoSubmit(true); // Flag to auto-submit after mounting
      }
    }, 200);

    return () => clearTimeout(timer);
  }, []);

  // Auto-submit the form if we have a pending prompt
  useEffect(() => {
    if (autoSubmit && inputValue && !isSubmitting) {
      const timer = setTimeout(() => {
        handleSubmit(inputValue);
        setAutoSubmit(false);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [autoSubmit, inputValue, isSubmitting]);

  return (
    <div className="flex flex-col items-center justify-center h-full w-full">
      {isMobile && (
        <div className="absolute top-4 left-4 z-10">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => setOpenMobile(true)}
              >
                <Menu className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>Open menu</TooltipContent>
          </Tooltip>
        </div>
      )}

      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[650px] max-w-[90%]">
        <div className="flex flex-col items-center text-center mb-2 w-full">
          <h1 className={cn('tracking-tight text-4xl font-semibold leading-tight')}>
            Hey{userFirstName ? ` ${userFirstName}` : ''}
          </h1>
          <p className="tracking-tight text-3xl font-normal text-muted-foreground/80 mt-2 flex items-center gap-2">
            What would you like Atlas to do today?
          </p>
        </div>

        <ChatInput
          ref={chatInputRef}
          onSubmit={handleSubmit}
          placeholder="Start a conversation..."
          loading={isSubmitting}
          value={inputValue}
          onChange={setInputValue}
          hideAttachments={false}
        />
      </div>

      {/* Billing Error Alert */}
      <BillingErrorAlert
        message={billingError?.message}
        currentUsage={billingError?.currentUsage}
        limit={billingError?.limit}
        accountId={personalAccount?.account_id}
        onDismiss={clearBillingError}
        isOpen={!!billingError}
      />
    </div>
  );
}

export default function DashboardPage() {
  return (
    <Suspense
      fallback={
        <div className="flex flex-col items-center justify-center h-full w-full">
          <AnimatedLoadingSkeleton variant="dashboard" />
        </div>
      }
    >
      <DashboardContent />
    </Suspense>
  );
}
