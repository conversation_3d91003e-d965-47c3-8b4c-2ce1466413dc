import { IconType } from 'react-icons';
import {
  SiGmail,
  SiMongodb,
  SiFirebase,
  SiGithub,
  SiSlack,
  SiDiscord,
  SiGoogle,
  SiMicrosoftazure,
  SiOpenai,
  SiAmazonaws,
  SiNotion,
  SiGoogledrive,
} from 'react-icons/si';
import { LucideIcon } from 'lucide-react';
import { Globe, Plug, Wrench, Search, Bot, FileEdit, FileSearch, FilePlus, FileText, Terminal, Network, FileX, CloudUpload, Code, MessageSquare, Cog } from 'lucide-react';
import { McpIcon } from '@/components/ui/mcp-icon';
import { FC } from 'react';

type IconComponent = IconType | LucideIcon | FC<any>;

// Map of tool name prefixes to their corresponding icons
const TOOL_ICON_MAP: Record<string, IconComponent> = {
  // Third-party integrations
  'gmail': SiGmail,
  'gmail-mcp': SiGmail,
  'gmail_mcp_tool': SiGmail,
  'notion': SiNotion,
  'notion-mcp': SiNotion,
  'notion_mcp_tool': SiNotion,
  'google-drive': SiGoogledrive,
  'google_drive': SiGoogledrive,
  'google-drive-mcp': SiGoogledrive,
  'googledrive_mcp_tool': SiGoogledrive,
  'slack': SiSlack,
  'slack-mcp': SiSlack,
  'slack_mcp_tool': SiSlack,
  'mongodb': SiMongodb,
  'firebase': SiFirebase,
  'github': SiGithub,
  'discord': SiDiscord,
  'google': SiGoogle,
  'azure': SiMicrosoftazure,
  'openai': SiOpenai,
  'aws': SiAmazonaws,

  // Generic icons for different types of tools
  'web': Globe,
  'api': Plug,
  'tool': Wrench,
  'search': Search,
  'ai': Bot,
  'edit': FileEdit,
  'find': FileSearch,
  'create': FilePlus,
  'read': FileText,
  'terminal': Terminal,
  'network': Network,
  'delete': FileX,
  'upload': CloudUpload,
  'code': Code,
  'chat': MessageSquare,
  'settings': Cog,
};

// Function to get the appropriate icon for a tool
export function getToolIcon(toolName: string): IconComponent {
  const normalizedName = toolName.toLowerCase();

  // First check for exact matches
  if (TOOL_ICON_MAP[normalizedName]) {
    return TOOL_ICON_MAP[normalizedName];
  }

  // Then check for prefix matches
  for (const [prefix, icon] of Object.entries(TOOL_ICON_MAP)) {
    if (normalizedName.startsWith(prefix)) {
      return icon;
    }
  }

  // Default to wrench icon if no match found
  return Wrench;
}
