"""
Composio Integration Configuration

This module defines the available Composio integrations and their metadata.
It provides a centralized configuration for all supported services.
"""

from typing import Dict, List, Optional
from dataclasses import dataclass


@dataclass
class ComposioIntegration:
    """Configuration for a Composio integration."""

    service_name: str
    display_name: str
    description: str
    category: str
    integration_id: str
    auth_type: str = "oauth2"
    icon_url: Optional[str] = None
    available: bool = True


# Composio Integration Definitions
# These match the services available in Composio's platform
COMPOSIO_INTEGRATIONS = {
    "gmail": ComposioIntegration(
        service_name="gmail",
        display_name="Gmail",
        description="Send and manage emails through Gmail",
        category="communication",
        integration_id="c806ea76-3258-4c41-a9a9-784a77fad00d",
        icon_url="https://developers.google.com/gmail/images/gmail-icon.png",
    ),
    "notion": ComposioIntegration(
        service_name="notion",
        display_name="Notion",
        description="Create and manage pages, databases, and content in Notion",
        category="productivity",
        integration_id="92ae7e87-2f68-4401-b63e-403115a4af59",
        icon_url="https://www.notion.so/images/logo-ios.png",
    ),
    "github": ComposioIntegration(
        service_name="github",
        display_name="GitHub",
        description="Manage repositories, issues, and pull requests on GitHub",
        category="development",
        integration_id="github",
        icon_url="https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png",
    ),
    "slack": ComposioIntegration(
        service_name="slack",
        display_name="Slack",
        description="Send messages and manage channels in Slack",
        category="communication",
        integration_id="4325a676-3a88-4ef8-9eb6-234817a867c7",
        icon_url="https://a.slack-edge.com/80588/marketing/img/icons/icon_slack_hash_colored.png",
    ),
    "google_drive": ComposioIntegration(
        service_name="google_drive",
        display_name="Google Drive",
        description="Manage files and folders in Google Drive",
        category="storage",
        integration_id="e4dea9b2-9b4b-4fe7-a267-9dc0de27b393",  # Correct Google Drive integration ID
        icon_url="https://ssl.gstatic.com/docs/doclist/images/drive_2022q3_32dp.png",
    ),
    "google_calendar": ComposioIntegration(
        service_name="google_calendar",
        display_name="Google Calendar",
        description="Create and manage events in Google Calendar",
        category="productivity",
        integration_id="googlecalendar",
        icon_url="https://calendar.google.com/googlecalendar/images/favicons_2020q4/calendar_31.ico",
    ),
    "google_docs": ComposioIntegration(
        service_name="google_docs",
        display_name="Google Docs",
        description="Create and edit documents in Google Docs",
        category="productivity",
        integration_id="googledocs",
        icon_url="https://ssl.gstatic.com/docs/documents/images/kix-favicon7.ico",
    ),
    "google_sheets": ComposioIntegration(
        service_name="google_sheets",
        display_name="Google Sheets",
        description="Create and manage spreadsheets in Google Sheets",
        category="productivity",
        integration_id="googlesheets",
        icon_url="https://ssl.gstatic.com/docs/spreadsheets/favicon3.ico",
    ),
    "trello": ComposioIntegration(
        service_name="trello",
        display_name="Trello",
        description="Manage boards, lists, and cards in Trello",
        category="productivity",
        integration_id="trello",
        icon_url="https://d2k1ftgv7pobq7.cloudfront.net/meta/c/p/res/images/trello-meta-logo.png",
    ),
    "asana": ComposioIntegration(
        service_name="asana",
        display_name="Asana",
        description="Manage projects and tasks in Asana",
        category="productivity",
        integration_id="asana",
        icon_url="https://luna1.co/eb0187.png",
    ),
    "linear": ComposioIntegration(
        service_name="linear",
        display_name="Linear",
        description="Manage issues and projects in Linear",
        category="development",
        integration_id="linear",
        icon_url="https://linear.app/favicon.ico",
    ),
    "jira": ComposioIntegration(
        service_name="jira",
        display_name="Jira",
        description="Manage issues and projects in Jira",
        category="development",
        integration_id="jira",
        icon_url="https://wac-cdn.atlassian.com/assets/img/favicons/atlassian/favicon.png",
    ),
    "discord": ComposioIntegration(
        service_name="discord",
        display_name="Discord",
        description="Send messages and manage servers in Discord",
        category="communication",
        integration_id="discord",
        icon_url="https://discord.com/assets/f8389ca1a741a115313bede9ac02e2c0.svg",
    ),
    "twitter": ComposioIntegration(
        service_name="twitter",
        display_name="Twitter/X",
        description="Post tweets and manage Twitter account",
        category="social",
        integration_id="twitter",
        icon_url="https://abs.twimg.com/favicons/twitter.2.ico",
    ),
    "linkedin": ComposioIntegration(
        service_name="linkedin",
        display_name="LinkedIn",
        description="Manage LinkedIn profile and connections",
        category="social",
        integration_id="linkedin",
        icon_url="https://static.licdn.com/sc/h/al2o9zrvru7aqj8e1x2rzsrca",
    ),
    "hubspot": ComposioIntegration(
        service_name="hubspot",
        display_name="HubSpot",
        description="Manage CRM data and marketing in HubSpot",
        category="crm",
        integration_id="hubspot",
        icon_url="https://www.hubspot.com/hubfs/HubSpot_Logos/HubSpot-Inversed-Favicon.png",
    ),
    "salesforce": ComposioIntegration(
        service_name="salesforce",
        display_name="Salesforce",
        description="Manage CRM data and sales processes in Salesforce",
        category="crm",
        integration_id="salesforce",
        icon_url="https://c1.sfdcstatic.com/etc/clientlibs/sfdc-aem-master/clientlibs_base/img/favicons/favicon-196x196.png",
    ),
    "zoom": ComposioIntegration(
        service_name="zoom",
        display_name="Zoom",
        description="Schedule and manage Zoom meetings",
        category="communication",
        integration_id="zoom",
        icon_url="https://st1.zoom.us/zoom.ico",
    ),
    "clickup": ComposioIntegration(
        service_name="clickup",
        display_name="ClickUp",
        description="Manage tasks, projects, and workflows in ClickUp",
        category="productivity",
        integration_id="clickup",
        icon_url="https://clickup.com/landing/images/brand/clickup-symbol_color.svg",
    ),
    "microsoft_teams": ComposioIntegration(
        service_name="microsoft_teams",
        display_name="Microsoft Teams",
        description="Send messages and manage teams in Microsoft Teams",
        category="communication",
        integration_id="microsoftteams",
        icon_url="https://res.cdn.office.net/teams/1.3.00.4461/images/app-icon.png",
    ),
    "dropbox": ComposioIntegration(
        service_name="dropbox",
        display_name="Dropbox",
        description="Manage files and folders in Dropbox",
        category="storage",
        integration_id="dropbox",
        icon_url="https://cfl.dropboxstatic.com/static/images/favicon-vfl8lUR9B.ico",
    ),
    "onedrive": ComposioIntegration(
        service_name="onedrive",
        display_name="OneDrive",
        description="Manage files and folders in OneDrive",
        category="storage",
        integration_id="onedrive",
        icon_url="https://res.cdn.office.net/onedrive/onedrive_2019/favicon.ico",
    ),
    "box": ComposioIntegration(
        service_name="box",
        display_name="Box",
        description="Manage files and folders in Box",
        category="storage",
        integration_id="box",
        icon_url="https://account.box.com/favicon.ico",
    ),
}


def get_integration(service_name: str) -> Optional[ComposioIntegration]:
    """
    Get integration configuration by service name.

    Supports both canonical names and common aliases for user-friendly access.

    Args:
        service_name: Name of the service

    Returns:
        ComposioIntegration instance or None if not found
    """
    # Service name aliases for user-friendly access
    SERVICE_ALIASES = {
        "googledrive": "google_drive",
        "googlecalendar": "google_calendar",
        "googledocs": "google_docs",
        "googlesheets": "google_sheets",
        "microsoftteams": "microsoft_teams",
    }

    # Normalize service name
    normalized_name = service_name.lower()

    # Check if it's an alias and map to canonical name
    if normalized_name in SERVICE_ALIASES:
        normalized_name = SERVICE_ALIASES[normalized_name]

    return COMPOSIO_INTEGRATIONS.get(normalized_name)


def get_all_integrations() -> List[ComposioIntegration]:
    """
    Get all available integrations.

    Returns:
        List of all ComposioIntegration instances
    """
    return list(COMPOSIO_INTEGRATIONS.values())


def get_integrations_by_category(category: str) -> List[ComposioIntegration]:
    """
    Get integrations filtered by category.

    Args:
        category: Category to filter by (e.g., "communication", "productivity")

    Returns:
        List of ComposioIntegration instances in the specified category
    """
    return [
        integration
        for integration in COMPOSIO_INTEGRATIONS.values()
        if integration.category.lower() == category.lower()
    ]


def get_available_categories() -> List[str]:
    """
    Get all available categories.

    Returns:
        List of unique category names
    """
    categories = set(
        integration.category for integration in COMPOSIO_INTEGRATIONS.values()
    )
    return sorted(list(categories))


def is_service_supported(service_name: str) -> bool:
    """
    Check if a service is supported.

    Supports both canonical names and common aliases.

    Args:
        service_name: Name of the service to check

    Returns:
        True if service is supported, False otherwise
    """
    integration = get_integration(service_name)
    return integration is not None and integration.available
