#!/usr/bin/env python3
"""
Test which ClickUp actions actually work with Composio's backend.

This script will test each ClickUp action to see if it has metadata
and can be used with Composio's toolset.
"""

import sys
import os
import asyncio

async def test_clickup_actions_with_toolset():
    """Test ClickUp actions with actual Composio toolset."""
    print("🧪 Testing ClickUp actions with Composio toolset...")
    
    try:
        from composio_openai import ComposioToolSet, Action, App
        
        # Create a toolset
        toolset = ComposioToolSet()
        
        # Test user ID
        test_user_id = "f7f2d110-7af2-41b1-a23c-3c6a3c866689"
        
        print(f"   👤 Testing with user: {test_user_id}")
        
        # Try to get all ClickUp tools
        print("   🔧 Getting all ClickUp tools...")
        try:
            all_clickup_tools = toolset.get_tools(apps=[App.CLICKUP])
            print(f"   ✅ Got {len(all_clickup_tools)} ClickUp tools from get_tools()")
            
            # Extract action names
            action_names = []
            for tool in all_clickup_tools:
                if isinstance(tool, dict) and "function" in tool:
                    action_names.append(tool["function"]["name"])
            
            print(f"   📋 Available ClickUp actions ({len(action_names)}):")
            for action in sorted(action_names):
                print(f"      ✅ {action}")
            
            return action_names
            
        except Exception as e:
            print(f"   ❌ Failed to get ClickUp tools: {e}")
            return []
        
    except Exception as e:
        print(f"❌ Toolset test failed: {e}")
        return []

async def test_specific_actions_with_schema():
    """Test specific actions to see if they have schemas."""
    print("\n🔍 Testing specific actions for schema availability...")
    
    # Actions that are failing
    problem_actions = [
        "CLICKUP_GET_MEMBERS",
        "CLICKUP_CREATE_TASK", 
        "CLICKUP_GET_TASKS",
        "CLICKUP_UPDATE_TASK",
    ]
    
    try:
        from composio_openai import ComposioToolSet, Action
        
        toolset = ComposioToolSet()
        
        working_actions = []
        failing_actions = []
        
        for action_name in problem_actions:
            try:
                print(f"   🧪 Testing {action_name}...")
                
                # Get the action enum
                action_enum = getattr(Action, action_name)
                
                # Try to get schema
                schemas = toolset.get_action_schemas(
                    actions=[action_enum],
                    check_connected_accounts=False
                )
                
                if schemas:
                    print(f"      ✅ {action_name}: Schema available")
                    working_actions.append(action_name)
                else:
                    print(f"      ❌ {action_name}: No schema returned")
                    failing_actions.append(action_name)
                
            except Exception as e:
                print(f"      ❌ {action_name}: Error - {e}")
                failing_actions.append(action_name)
        
        print(f"\n📊 Schema test results:")
        print(f"   ✅ Working actions: {len(working_actions)}")
        print(f"   ❌ Failing actions: {len(failing_actions)}")
        
        if failing_actions:
            print(f"\n🚫 Actions to remove from configuration:")
            for action in failing_actions:
                print(f"   - {action}")
        
        return working_actions, failing_actions
        
    except Exception as e:
        print(f"❌ Schema test failed: {e}")
        return [], []

async def test_minimal_clickup_actions():
    """Test with a minimal set of ClickUp actions."""
    print("\n🎯 Testing minimal ClickUp action set...")
    
    # Start with just the most basic actions
    minimal_actions = [
        "CLICKUP_CREATE_TASK",
        "CLICKUP_GET_TASKS", 
        "CLICKUP_UPDATE_TASK",
    ]
    
    try:
        from composio_openai import ComposioToolSet, Action
        
        toolset = ComposioToolSet()
        
        # Convert to action enums
        action_enums = []
        for action_name in minimal_actions:
            try:
                action_enum = getattr(Action, action_name)
                action_enums.append(action_enum)
            except AttributeError:
                print(f"   ❌ {action_name}: Not found in Action enum")
        
        if action_enums:
            print(f"   🔧 Testing {len(action_enums)} minimal actions...")
            
            try:
                tools = toolset.get_tools(actions=action_enums)
                print(f"   ✅ Successfully got {len(tools)} tools for minimal actions")
                
                # Extract action names
                action_names = []
                for tool in tools:
                    if isinstance(tool, dict) and "function" in tool:
                        action_names.append(tool["function"]["name"])
                
                print(f"   📋 Working minimal actions:")
                for action in action_names:
                    print(f"      ✅ {action}")
                
                return action_names
                
            except Exception as e:
                print(f"   ❌ Failed to get tools for minimal actions: {e}")
                return []
        else:
            print("   ❌ No valid action enums found")
            return []
        
    except Exception as e:
        print(f"❌ Minimal test failed: {e}")
        return []

async def main():
    """Main testing function."""
    print("🚀 ClickUp Real Action Testing")
    print("=" * 60)
    
    # Test with toolset
    available_actions = await test_clickup_actions_with_toolset()
    
    # Test specific actions for schemas
    working_actions, failing_actions = await test_specific_actions_with_schema()
    
    # Test minimal set
    minimal_working = await test_minimal_clickup_actions()
    
    print("\n" + "=" * 60)
    print("📊 REAL ACTION TEST SUMMARY")
    print("=" * 60)
    print(f"Available actions from get_tools(): {len(available_actions)}")
    print(f"Actions with working schemas: {len(working_actions)}")
    print(f"Actions with failing schemas: {len(failing_actions)}")
    print(f"Minimal working actions: {len(minimal_working)}")
    
    if available_actions:
        print(f"\n✅ Recommended ClickUp actions to use:")
        for action in available_actions[:10]:  # Show first 10
            print(f"   - {action}")
        if len(available_actions) > 10:
            print(f"   ... and {len(available_actions) - 10} more")
    
    if failing_actions:
        print(f"\n🚫 Actions to remove from configuration:")
        for action in failing_actions:
            print(f"   - {action}")
    
    print(f"\n💡 Next Steps:")
    if available_actions:
        print("1. Use the actions from get_tools() as the definitive list")
        print("2. Update configuration files with working actions only")
        print("3. Test the integration again")
    else:
        print("1. ClickUp integration may not be fully available")
        print("2. Check Composio documentation for ClickUp status")
        print("3. Consider using minimal action set for testing")
    
    return available_actions

if __name__ == "__main__":
    working_actions = asyncio.run(main())
