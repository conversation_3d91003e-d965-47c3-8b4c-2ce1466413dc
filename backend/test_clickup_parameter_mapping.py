#!/usr/bin/env python3
"""
Test ClickUp Parameter Mapping

This script tests that ClickUp parameter mappings work correctly,
converting user-friendly parameter names to ClickUp API parameter names.
"""

import sys
import os

def test_clickup_parameter_mappings():
    """Test that ClickUp parameter mappings are correctly defined."""
    print("🔧 Testing ClickUp parameter mappings...")
    
    try:
        from services.composio_processors import PARAMETER_MAPPINGS, create_parameter_mapper
        
        # Check if ClickUp mappings exist
        if "clickup" not in PARAMETER_MAPPINGS:
            print("❌ ClickUp not found in PARAMETER_MAPPINGS")
            return False
        
        clickup_mappings = PARAMETER_MAPPINGS["clickup"]
        print(f"✅ Found ClickUp mappings for {len(clickup_mappings)} actions:")
        
        for action, mappings in clickup_mappings.items():
            print(f"   📋 {action}: {len(mappings)} parameter mappings")
        
        return True
        
    except Exception as e:
        print(f"❌ Parameter mapping test failed: {e}")
        return False

def test_create_task_parameter_mapping():
    """Test CLICKUP_CREATE_TASK parameter mapping specifically."""
    print("\n🎯 Testing CLICKUP_CREATE_TASK parameter mapping...")
    
    try:
        from services.composio_processors import create_parameter_mapper
        
        # Create parameter mapper for ClickUp CREATE_TASK
        mapper = create_parameter_mapper("clickup", "CLICKUP_CREATE_TASK")
        
        # Test input with user-friendly parameter names
        test_input = {
            "title": "finish product",  # Should map to "name"
            "content": "Complete the product development",  # Should map to "description"
            "list": "123456789",  # Should map to "list_id"
            "assignee": "user123",  # Should map to "assignees" (and convert to array)
            "due": "2024-12-31",  # Should map to "due_date"
            "tag": "urgent",  # Should map to "tags" (and convert to array)
        }
        
        print(f"   📥 Input parameters: {test_input}")
        
        # Apply parameter mapping
        mapped_output = mapper(test_input)
        
        print(f"   📤 Mapped parameters: {mapped_output}")
        
        # Verify mappings
        expected_mappings = {
            "name": "finish product",
            "description": "Complete the product development", 
            "list_id": "123456789",
            "assignees": ["user123"],  # Should be converted to array
            "due_date": "2024-12-31",
            "tags": ["urgent"],  # Should be converted to array
        }
        
        success = True
        for expected_key, expected_value in expected_mappings.items():
            if expected_key not in mapped_output:
                print(f"   ❌ Missing expected parameter: {expected_key}")
                success = False
            elif mapped_output[expected_key] != expected_value:
                print(f"   ❌ Parameter {expected_key}: expected {expected_value}, got {mapped_output[expected_key]}")
                success = False
            else:
                print(f"   ✅ Parameter {expected_key}: correctly mapped")
        
        return success
        
    except Exception as e:
        print(f"❌ CREATE_TASK parameter mapping test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_create_task_comment_mapping():
    """Test CLICKUP_CREATE_TASK_COMMENT parameter mapping."""
    print("\n💬 Testing CLICKUP_CREATE_TASK_COMMENT parameter mapping...")
    
    try:
        from services.composio_processors import create_parameter_mapper
        
        # Create parameter mapper for ClickUp CREATE_TASK_COMMENT
        mapper = create_parameter_mapper("clickup", "CLICKUP_CREATE_TASK_COMMENT")
        
        # Test input with user-friendly parameter names
        test_input = {
            "task": "task123",  # Should map to "task_id"
            "message": "This is a comment",  # Should map to "comment_text"
            "assignee": "user456",
        }
        
        print(f"   📥 Input parameters: {test_input}")
        
        # Apply parameter mapping
        mapped_output = mapper(test_input)
        
        print(f"   📤 Mapped parameters: {mapped_output}")
        
        # Verify mappings
        expected_mappings = {
            "task_id": "task123",
            "comment_text": "This is a comment",
            "assignee": "user456",
            "notify_all": True,  # Should be added as default
        }
        
        success = True
        for expected_key, expected_value in expected_mappings.items():
            if expected_key not in mapped_output:
                print(f"   ❌ Missing expected parameter: {expected_key}")
                success = False
            elif mapped_output[expected_key] != expected_value:
                print(f"   ❌ Parameter {expected_key}: expected {expected_value}, got {mapped_output[expected_key]}")
                success = False
            else:
                print(f"   ✅ Parameter {expected_key}: correctly mapped")
        
        return success
        
    except Exception as e:
        print(f"❌ CREATE_TASK_COMMENT parameter mapping test failed: {e}")
        return False

def test_missing_list_id_warning():
    """Test that missing list_id generates appropriate warning."""
    print("\n⚠️ Testing missing list_id warning...")
    
    try:
        from services.composio_processors import create_parameter_mapper
        import logging
        
        # Set up logging to capture warnings
        logging.basicConfig(level=logging.WARNING)
        
        # Create parameter mapper for ClickUp CREATE_TASK
        mapper = create_parameter_mapper("clickup", "CLICKUP_CREATE_TASK")
        
        # Test input WITHOUT list_id
        test_input = {
            "name": "Test Task",
            "description": "Test description",
        }
        
        print(f"   📥 Input parameters (no list_id): {test_input}")
        
        # Apply parameter mapping (should generate warning)
        mapped_output = mapper(test_input)
        
        print(f"   📤 Mapped parameters: {mapped_output}")
        
        # Check that the warning was logged and parameters are still processed
        if "name" in mapped_output and mapped_output["name"] == "Test Task":
            print("   ✅ Parameters processed despite missing list_id")
            print("   ✅ Warning should have been logged about missing list_id")
            return True
        else:
            print("   ❌ Parameters not processed correctly")
            return False
        
    except Exception as e:
        print(f"❌ Missing list_id warning test failed: {e}")
        return False

def main():
    """Run all ClickUp parameter mapping tests."""
    print("🚀 ClickUp Parameter Mapping Tests")
    print("=" * 60)
    
    test_results = {
        "parameter_mappings": test_clickup_parameter_mappings(),
        "create_task_mapping": test_create_task_parameter_mapping(),
        "create_comment_mapping": test_create_task_comment_mapping(),
        "missing_list_id_warning": test_missing_list_id_warning(),
    }
    
    print("\n" + "=" * 60)
    print("📊 PARAMETER MAPPING TEST RESULTS")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL PARAMETER MAPPING TESTS PASSED!")
        print("\n💡 ClickUp parameter mappings are working correctly.")
        print("\n📋 Next Steps:")
        print("1. Test with actual ClickUp API calls")
        print("2. Verify that list_id is provided by users or discovered automatically")
        print("3. Test other ClickUp actions (CREATE_LIST, CREATE_FOLDER, etc.)")
    else:
        print("❌ SOME PARAMETER MAPPING TESTS FAILED!")
        print("\n💡 Check the failed tests and fix parameter mappings.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
