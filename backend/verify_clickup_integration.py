#!/usr/bin/env python3
"""
Simple verification script for ClickUp integration.
"""

import sys
import os

def test_clickup_processors():
    """Test that ClickUp processors are properly configured."""
    print("🔧 Testing ClickUp processors...")
    
    try:
        from services.composio_processors import get_clickup_processors, get_all_processors
        
        # Test ClickUp processors
        clickup_procs = get_clickup_processors()
        print(f"✅ ClickUp processors created: {len(clickup_procs['schema'])} schema processors")
        
        # Test all processors include ClickUp
        all_procs = get_all_processors()
        clickup_actions_in_all = [action for action in all_procs['schema'].keys() 
                                 if str(action).startswith('CLICKUP_')]
        print(f"✅ ClickUp actions in all processors: {len(clickup_actions_in_all)}")
        
        return True
        
    except Exception as e:
        print(f"❌ ClickUp processors test failed: {e}")
        return False

def test_clickup_service_actions():
    """Test that ClickUp actions are in service configurations."""
    print("\n🔧 Testing ClickUp service actions...")
    
    try:
        from services.composio_openai_service import ComposioXMLService
        
        # Create service instance
        service = ComposioXMLService.from_env()
        
        # Get ClickUp actions
        clickup_actions = service._get_service_actions("clickup")
        print(f"✅ Found {len(clickup_actions)} ClickUp actions")
        
        for action in clickup_actions[:5]:  # Show first 5
            print(f"   📋 {action}")
        
        if len(clickup_actions) > 5:
            print(f"   ... and {len(clickup_actions) - 5} more")
        
        return len(clickup_actions) > 0
        
    except Exception as e:
        print(f"❌ ClickUp service actions test failed: {e}")
        return False

def test_clickup_integration_config():
    """Test that ClickUp is in integration configuration."""
    print("\n🔧 Testing ClickUp integration configuration...")
    
    try:
        from config.composio_integrations import get_integration, COMPOSIO_INTEGRATIONS
        
        # Test ClickUp integration
        clickup_integration = get_integration("clickup")
        if clickup_integration:
            print(f"✅ ClickUp integration found: {clickup_integration.display_name}")
            print(f"   📋 Integration ID: {clickup_integration.integration_id}")
            print(f"   📂 Category: {clickup_integration.category}")
            return True
        else:
            print("❌ ClickUp integration not found")
            return False
        
    except Exception as e:
        print(f"❌ ClickUp integration config test failed: {e}")
        return False

def test_clickup_auth_mapping():
    """Test that ClickUp is in auth service mapping."""
    print("\n🔧 Testing ClickUp auth service mapping...")
    
    try:
        # Check if the mapping exists in the source code
        with open('services/composio_auth_service.py', 'r') as f:
            content = f.read()
            
        if '"clickup": "CLICKUP"' in content:
            print("✅ ClickUp auth mapping found in composio_auth_service.py")
            return True
        else:
            print("❌ ClickUp auth mapping not found")
            return False
        
    except Exception as e:
        print(f"❌ ClickUp auth mapping test failed: {e}")
        return False

def main():
    """Run all verification tests."""
    print("🚀 ClickUp Integration Verification")
    print("=" * 50)
    
    test_results = {
        "processors": test_clickup_processors(),
        "service_actions": test_clickup_service_actions(),
        "integration_config": test_clickup_integration_config(),
        "auth_mapping": test_clickup_auth_mapping(),
    }
    
    print("\n" + "=" * 50)
    print("📊 VERIFICATION RESULTS")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL TESTS PASSED! ClickUp integration is properly configured.")
        print("\n💡 Next Steps:")
        print("1. Test with real ClickUp credentials")
        print("2. Run end-to-end agent tests")
        print("3. Verify frontend integration display")
    else:
        print("❌ SOME TESTS FAILED! Please check the configuration.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
