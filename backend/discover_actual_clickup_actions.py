#!/usr/bin/env python3
"""
Discover actual ClickUp actions available in Composio.

This script will check which ClickUp actions actually exist in the Composio Action enum
and remove the ones that don't exist.
"""

import sys
import os

def discover_actual_clickup_actions():
    """Discover which ClickUp actions actually exist in Composio."""
    print("🔍 Discovering actual ClickUp actions in Composio...")
    
    try:
        from composio_openai import Action
        
        # Get all attributes from Action enum
        all_actions = [attr for attr in dir(Action) if not attr.startswith('_')]
        
        # Filter for ClickUp actions
        clickup_actions = [action for action in all_actions if action.startswith('CLICKUP_')]
        
        print(f"✅ Found {len(clickup_actions)} actual ClickUp actions in Composio:")
        for action in sorted(clickup_actions):
            print(f"   📋 {action}")
        
        return clickup_actions
        
    except Exception as e:
        print(f"❌ Discovery failed: {e}")
        return []

def test_specific_actions():
    """Test specific actions that we're trying to use."""
    print("\n🧪 Testing specific ClickUp actions...")
    
    # Actions we're currently trying to use
    test_actions = [
        "CLICKUP_CREATE_TASK",
        "CLICKUP_GET_TASKS", 
        "CLICKUP_UPDATE_TASK",
        "CLICKUP_GET_LISTS",
        "CLICKUP_GET_SPACES",
        "CLICKUP_GET_TEAMS",
        "CLICKUP_DELETE_TASK",
        "CLICKUP_GET_TASK",
        "CLICKUP_CREATE_LIST",
        "CLICKUP_UPDATE_LIST",
        "CLICKUP_DELETE_LIST",
        "CLICKUP_CREATE_SPACE",
        "CLICKUP_UPDATE_SPACE", 
        "CLICKUP_DELETE_SPACE",
        "CLICKUP_GET_MEMBERS",  # This one is failing
        "CLICKUP_ADD_MEMBER",
        "CLICKUP_REMOVE_MEMBER",
        "CLICKUP_CREATE_COMMENT",
        "CLICKUP_GET_COMMENTS",
        "CLICKUP_UPDATE_COMMENT",
        "CLICKUP_DELETE_COMMENT",
        "CLICKUP_CREATE_CHECKLIST",
        "CLICKUP_GET_CHECKLISTS",
        "CLICKUP_UPDATE_CHECKLIST",
        "CLICKUP_DELETE_CHECKLIST",
        "CLICKUP_ADD_ATTACHMENT",
        "CLICKUP_GET_ATTACHMENTS",
        "CLICKUP_DELETE_ATTACHMENT",
        "CLICKUP_CREATE_GOAL",
        "CLICKUP_GET_GOALS",
        "CLICKUP_UPDATE_GOAL",
        "CLICKUP_DELETE_GOAL",
    ]
    
    try:
        from composio_openai import Action
        
        existing_actions = []
        missing_actions = []
        
        for action in test_actions:
            try:
                action_enum = getattr(Action, action)
                existing_actions.append(action)
                print(f"   ✅ {action}: EXISTS")
            except AttributeError:
                missing_actions.append(action)
                print(f"   ❌ {action}: NOT FOUND")
        
        print(f"\n📊 Results:")
        print(f"   ✅ Existing actions: {len(existing_actions)}")
        print(f"   ❌ Missing actions: {len(missing_actions)}")
        
        if missing_actions:
            print(f"\n🚫 Actions to remove from our configuration:")
            for action in missing_actions:
                print(f"   - {action}")
        
        if existing_actions:
            print(f"\n✅ Actions to keep in our configuration:")
            for action in existing_actions:
                print(f"   - {action}")
        
        return existing_actions, missing_actions
        
    except Exception as e:
        print(f"❌ Testing failed: {e}")
        return [], []

def generate_corrected_action_list():
    """Generate a corrected action list for ClickUp."""
    print("\n🔧 Generating corrected ClickUp action list...")
    
    existing_actions, missing_actions = test_specific_actions()
    
    if existing_actions:
        print(f"\n📝 Corrected ClickUp action list ({len(existing_actions)} actions):")
        print("clickup_actions = [")
        for action in existing_actions:
            print(f'    "{action}",')
        print("]")
        
        return existing_actions
    else:
        print("❌ No valid ClickUp actions found")
        return []

def main():
    """Main discovery function."""
    print("🚀 ClickUp Action Discovery and Correction")
    print("=" * 60)
    
    # Discover all ClickUp actions
    all_clickup_actions = discover_actual_clickup_actions()
    
    # Test our specific actions
    existing_actions, missing_actions = test_specific_actions()
    
    # Generate corrected list
    corrected_actions = generate_corrected_action_list()
    
    print("\n" + "=" * 60)
    print("📊 DISCOVERY SUMMARY")
    print("=" * 60)
    print(f"Total ClickUp actions in Composio: {len(all_clickup_actions)}")
    print(f"Actions we're using that exist: {len(existing_actions)}")
    print(f"Actions we're using that DON'T exist: {len(missing_actions)}")
    
    if missing_actions:
        print(f"\n🔥 URGENT: Remove these {len(missing_actions)} actions from configuration:")
        for action in missing_actions:
            print(f"   ❌ {action}")
    
    print(f"\n💡 Next Steps:")
    print("1. Update composio_openai_service.py with corrected action list")
    print("2. Update composio_processors.py with corrected action list") 
    print("3. Test the integration again")
    
    return corrected_actions

if __name__ == "__main__":
    corrected_actions = main()
