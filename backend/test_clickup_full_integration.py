#!/usr/bin/env python3
"""
Comprehensive ClickUp Integration Test

This script tests that ClickUp is fully integrated across all components:
1. Backend service configurations
2. App enum mappings
3. Action processors
4. Tool factory support
5. Auth service mappings
6. Prompt examples
"""

import sys
import os
import asyncio

def test_app_enum_mapping():
    """Test that ClickUp is in the App enum mapping."""
    print("🔧 Testing App enum mapping...")
    
    try:
        from services.composio_openai_service import ComposioXMLService
        
        # Create service instance (without API key for testing)
        service = ComposioXMLService.__new__(ComposioXMLService)
        
        # Test the mapping method
        app_enum = service._get_app_enum("clickup")
        if app_enum:
            print(f"✅ ClickUp App enum found: {app_enum}")
            return True
        else:
            print("❌ ClickUp App enum not found")
            return False
        
    except Exception as e:
        print(f"❌ App enum mapping test failed: {e}")
        return False

def test_service_actions():
    """Test that ClickUp actions are configured."""
    print("\n🔧 Testing service actions...")
    
    try:
        from services.composio_openai_service import ComposioXMLService
        
        # Create service instance (without API key for testing)
        service = ComposioXMLService.__new__(ComposioXMLService)
        
        # Test the actions method
        actions = service._get_service_actions("clickup")
        print(f"✅ Found {len(actions)} ClickUp actions:")
        for action in actions[:5]:  # Show first 5
            print(f"   📋 {action}")
        if len(actions) > 5:
            print(f"   ... and {len(actions) - 5} more")
        
        return len(actions) > 0
        
    except Exception as e:
        print(f"❌ Service actions test failed: {e}")
        return False

def test_processors():
    """Test that ClickUp processors are configured."""
    print("\n🔧 Testing ClickUp processors...")
    
    try:
        from services.composio_processors import get_clickup_processors, get_all_processors
        
        # Test ClickUp processors
        clickup_procs = get_clickup_processors()
        print(f"✅ ClickUp processors created: {len(clickup_procs['schema'])} schema processors")
        
        # Test all processors include ClickUp
        all_procs = get_all_processors()
        clickup_actions_in_all = [action for action in all_procs['schema'].keys() 
                                 if str(action).startswith('CLICKUP_')]
        print(f"✅ ClickUp actions in all processors: {len(clickup_actions_in_all)}")
        
        return len(clickup_procs['schema']) > 0
        
    except Exception as e:
        print(f"❌ ClickUp processors test failed: {e}")
        return False

def test_xml_service():
    """Test that ClickUp is in XML service configurations."""
    print("\n🔧 Testing XML service configuration...")
    
    try:
        from services.composio_xml_service import EnhancedComposioXMLService
        
        # Create service instance (without API key for testing)
        service = EnhancedComposioXMLService.__new__(EnhancedComposioXMLService)
        
        # Test service actions method
        actions = asyncio.run(service.get_service_actions("clickup"))
        print(f"✅ XML service found {len(actions)} ClickUp actions:")
        for action in actions:
            print(f"   📋 {action}")
        
        return len(actions) > 0
        
    except Exception as e:
        print(f"❌ XML service test failed: {e}")
        return False

def test_tool_factory_support():
    """Test that ClickUp is supported by the tool factory."""
    print("\n🔧 Testing tool factory support...")
    
    try:
        from agent.tools.composio_xml_factory import ComposioXMLToolFactory
        
        # Create factory instance
        factory = ComposioXMLToolFactory()
        
        # Test supported services
        supported_services = factory.get_supported_services()
        if "clickup" in supported_services:
            print("✅ ClickUp is in supported services list")
            
            # Test service support check
            is_supported = factory.is_service_supported("clickup")
            if is_supported:
                print("✅ ClickUp service support check passed")
                return True
            else:
                print("❌ ClickUp service support check failed")
                return False
        else:
            print("❌ ClickUp not in supported services list")
            return False
        
    except Exception as e:
        print(f"❌ Tool factory support test failed: {e}")
        return False

def test_auth_service_mapping():
    """Test that ClickUp is in auth service mapping."""
    print("\n🔧 Testing auth service mapping...")
    
    try:
        # Check if the mapping exists in the source code
        with open('services/composio_auth_service.py', 'r') as f:
            content = f.read()
            
        if '"clickup": "CLICKUP"' in content:
            print("✅ ClickUp auth mapping found in composio_auth_service.py")
            return True
        else:
            print("❌ ClickUp auth mapping not found")
            return False
        
    except Exception as e:
        print(f"❌ Auth service mapping test failed: {e}")
        return False

def test_integration_config():
    """Test that ClickUp is in integration configuration."""
    print("\n🔧 Testing integration configuration...")
    
    try:
        from config.composio_integrations import get_integration, COMPOSIO_INTEGRATIONS
        
        # Test ClickUp integration
        clickup_integration = get_integration("clickup")
        if clickup_integration:
            print(f"✅ ClickUp integration found: {clickup_integration.display_name}")
            print(f"   📋 Integration ID: {clickup_integration.integration_id}")
            print(f"   📂 Category: {clickup_integration.category}")
            return True
        else:
            print("❌ ClickUp integration not found")
            return False
        
    except Exception as e:
        print(f"❌ Integration config test failed: {e}")
        return False

def test_prompt_examples():
    """Test that ClickUp is mentioned in prompt examples."""
    print("\n🔧 Testing prompt examples...")
    
    try:
        # Check prompt.py for ClickUp examples
        with open('agent/prompt.py', 'r') as f:
            prompt_content = f.read()
            
        # Check prompt.txt for ClickUp mentions
        with open('agent/prompt.txt', 'r') as f:
            prompt_txt_content = f.read()
        
        clickup_in_py = "clickup" in prompt_content.lower()
        clickup_in_txt = "clickup" in prompt_txt_content.lower()
        
        if clickup_in_py:
            print("✅ ClickUp found in prompt.py")
        else:
            print("❌ ClickUp not found in prompt.py")
            
        if clickup_in_txt:
            print("✅ ClickUp found in prompt.txt")
        else:
            print("❌ ClickUp not found in prompt.txt")
        
        return clickup_in_py and clickup_in_txt
        
    except Exception as e:
        print(f"❌ Prompt examples test failed: {e}")
        return False

def main():
    """Run all comprehensive tests."""
    print("🚀 Comprehensive ClickUp Integration Test")
    print("=" * 60)
    
    test_results = {
        "app_enum_mapping": test_app_enum_mapping(),
        "service_actions": test_service_actions(),
        "processors": test_processors(),
        "xml_service": test_xml_service(),
        "tool_factory_support": test_tool_factory_support(),
        "auth_service_mapping": test_auth_service_mapping(),
        "integration_config": test_integration_config(),
        "prompt_examples": test_prompt_examples(),
    }
    
    print("\n" + "=" * 60)
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! ClickUp is fully integrated across all components.")
        print("\n💡 ClickUp should now be available to the agent when users have active connections.")
        print("\n📋 Next Steps:")
        print("1. User needs to connect ClickUp account via frontend")
        print("2. Agent will automatically detect and load ClickUp tools")
        print("3. Agent can use ClickUp for task management operations")
    else:
        print("❌ SOME TESTS FAILED! Please check the failed components.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
