#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to discover ClickUp actions from Composio SDK.

This script will help us understand what ClickUp actions are available
and their parameter schemas so we can create proper mappings.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def discover_clickup_actions():
    """Discover available ClickUp actions from Composio."""
    print("🔍 Discovering ClickUp actions from Composio SDK...")
    
    try:
        from composio import Action, App
        from composio_openai import ComposioToolSet
        
        # Initialize toolset
        toolset = ComposioToolSet()
        
        # Try to get ClickUp app
        try:
            clickup_app = App.CLICKUP
            print(f"✅ Found ClickUp app: {clickup_app}")
        except AttributeError:
            print("❌ ClickUp app not found in Composio SDK")
            return False
        
        # Get all actions for ClickUp
        try:
            # Get tools for ClickUp app
            tools = toolset.get_tools(apps=[clickup_app])
            print(f"✅ Found {len(tools)} ClickUp tools")
            
            # Extract action information
            clickup_actions = []
            for tool in tools:
                if hasattr(tool, 'name') and 'CLICKUP' in tool.name.upper():
                    action_info = {
                        'name': tool.name,
                        'description': getattr(tool, 'description', 'No description'),
                        'parameters': getattr(tool, 'parameters', {})
                    }
                    clickup_actions.append(action_info)
                    print(f"   📋 {tool.name}: {action_info['description'][:100]}...")
            
            return clickup_actions
            
        except Exception as e:
            print(f"❌ Error getting ClickUp tools: {e}")
            
            # Try alternative approach - check Action enum
            print("🔄 Trying alternative approach - checking Action enum...")
            clickup_actions = []
            
            for attr_name in dir(Action):
                if attr_name.startswith('CLICKUP_'):
                    try:
                        action = getattr(Action, attr_name)
                        clickup_actions.append({
                            'name': attr_name,
                            'action_enum': action,
                            'description': f"ClickUp action: {attr_name}"
                        })
                        print(f"   📋 {attr_name}")
                    except Exception as action_error:
                        print(f"   ❌ Error with {attr_name}: {action_error}")
            
            if clickup_actions:
                print(f"✅ Found {len(clickup_actions)} ClickUp actions via Action enum")
                return clickup_actions
            else:
                print("❌ No ClickUp actions found via Action enum")
                return False
        
    except Exception as e:
        print(f"❌ Discovery failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def get_common_clickup_actions():
    """Get common ClickUp actions based on typical task management needs."""
    print("\n📝 Defining common ClickUp actions based on typical usage...")
    
    # Based on common ClickUp API patterns and task management needs
    common_actions = [
        {
            'name': 'CLICKUP_CREATE_TASK',
            'description': 'Create a new task in ClickUp',
            'parameters': {
                'name': {'type': 'string', 'required': True, 'description': 'Task name'},
                'description': {'type': 'string', 'required': False, 'description': 'Task description'},
                'list_id': {'type': 'string', 'required': True, 'description': 'List ID where task will be created'},
                'assignees': {'type': 'array', 'required': False, 'description': 'Array of user IDs to assign'},
                'priority': {'type': 'integer', 'required': False, 'description': 'Priority level (1-4)'},
                'due_date': {'type': 'string', 'required': False, 'description': 'Due date in Unix timestamp'},
                'status': {'type': 'string', 'required': False, 'description': 'Task status'},
                'tags': {'type': 'array', 'required': False, 'description': 'Array of tag names'}
            }
        },
        {
            'name': 'CLICKUP_GET_TASKS',
            'description': 'Get tasks from a ClickUp list',
            'parameters': {
                'list_id': {'type': 'string', 'required': True, 'description': 'List ID to get tasks from'},
                'archived': {'type': 'boolean', 'required': False, 'description': 'Include archived tasks'},
                'page': {'type': 'integer', 'required': False, 'description': 'Page number for pagination'},
                'order_by': {'type': 'string', 'required': False, 'description': 'Field to order by'},
                'reverse': {'type': 'boolean', 'required': False, 'description': 'Reverse order'},
                'subtasks': {'type': 'boolean', 'required': False, 'description': 'Include subtasks'},
                'statuses': {'type': 'array', 'required': False, 'description': 'Filter by status'},
                'include_closed': {'type': 'boolean', 'required': False, 'description': 'Include closed tasks'}
            }
        },
        {
            'name': 'CLICKUP_UPDATE_TASK',
            'description': 'Update an existing task in ClickUp',
            'parameters': {
                'task_id': {'type': 'string', 'required': True, 'description': 'Task ID to update'},
                'name': {'type': 'string', 'required': False, 'description': 'New task name'},
                'description': {'type': 'string', 'required': False, 'description': 'New task description'},
                'status': {'type': 'string', 'required': False, 'description': 'New task status'},
                'priority': {'type': 'integer', 'required': False, 'description': 'New priority level (1-4)'},
                'due_date': {'type': 'string', 'required': False, 'description': 'New due date in Unix timestamp'},
                'assignees': {'type': 'array', 'required': False, 'description': 'New array of user IDs to assign'}
            }
        },
        {
            'name': 'CLICKUP_GET_LISTS',
            'description': 'Get lists from a ClickUp space',
            'parameters': {
                'space_id': {'type': 'string', 'required': True, 'description': 'Space ID to get lists from'},
                'archived': {'type': 'boolean', 'required': False, 'description': 'Include archived lists'}
            }
        },
        {
            'name': 'CLICKUP_GET_SPACES',
            'description': 'Get spaces from a ClickUp workspace',
            'parameters': {
                'team_id': {'type': 'string', 'required': True, 'description': 'Team ID to get spaces from'},
                'archived': {'type': 'boolean', 'required': False, 'description': 'Include archived spaces'}
            }
        },
        {
            'name': 'CLICKUP_GET_TEAMS',
            'description': 'Get teams (workspaces) for the authenticated user',
            'parameters': {}
        }
    ]
    
    for action in common_actions:
        print(f"   📋 {action['name']}: {action['description']}")
    
    return common_actions

def main():
    """Main discovery function."""
    print("🚀 ClickUp Action Discovery")
    print("=" * 50)
    
    # Try to discover from SDK first
    discovered_actions = discover_clickup_actions()
    
    # Get common actions as fallback
    common_actions = get_common_clickup_actions()
    
    print("\n" + "=" * 50)
    print("📊 DISCOVERY RESULTS")
    print("=" * 50)
    
    if discovered_actions:
        print(f"✅ Discovered {len(discovered_actions)} actions from Composio SDK")
        print("   These are the actual available actions.")
    else:
        print("❌ Could not discover actions from Composio SDK")
        print("   Using common ClickUp actions as reference.")
    
    print(f"📝 Defined {len(common_actions)} common ClickUp actions")
    print("   These represent typical task management operations.")
    
    print("\n💡 Next Steps:")
    print("1. Use the discovered/common actions to create parameter mappings")
    print("2. Add ClickUp processors to composio_processors.py")
    print("3. Update service action lists in composio_openai_service.py")
    print("4. Create comprehensive tests for ClickUp integration")
    
    return discovered_actions or common_actions

if __name__ == "__main__":
    actions = main()
