#!/usr/bin/env python3
"""
Test Agent ClickUp Tool Visibility

This script tests that the agent can see and load ClickUp tools
when a user has an active ClickUp connection.
"""

import sys
import os
import asyncio

async def test_agent_tool_loading():
    """Test that the agent can load ClickUp tools for a user with connections."""
    print("🤖 Testing agent ClickUp tool visibility...")
    
    try:
        from agent.tools.composio_xml_factory import ComposioXMLToolFactory
        from services.composio_openai_service import ComposioXMLService
        
        # Test user ID from the CSV file
        test_user_id = "d4b8c8e0-1234-5678-9abc-def012345678"
        
        print(f"   👤 Testing with user ID: {test_user_id}")
        
        # Create factory
        factory = ComposioXMLToolFactory()
        
        # Test tool creation for user
        print("   🔧 Creating tools for user...")
        tools = await factory.create_user_tools(test_user_id)
        
        # Check if ClickUp tools are present
        clickup_tools = [tool for tool in tools if 'clickup' in str(tool).lower()]
        
        print(f"   📊 Total tools created: {len(tools)}")
        print(f"   📋 ClickUp tools found: {len(clickup_tools)}")
        
        if clickup_tools:
            print("   ✅ ClickUp tools are visible to the agent!")
            for tool in clickup_tools[:3]:  # Show first 3
                print(f"      🔧 {tool}")
            if len(clickup_tools) > 3:
                print(f"      ... and {len(clickup_tools) - 3} more")
            return True
        else:
            print("   ❌ No ClickUp tools found")
            print("   ℹ️ This could mean:")
            print("      - User has no active ClickUp connection")
            print("      - Connection is not properly configured")
            print("      - Service discovery is not working")
            return False
        
    except Exception as e:
        print(f"   ❌ Tool loading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_service_discovery():
    """Test that the service can discover user's active services."""
    print("\n🔍 Testing service discovery...")
    
    try:
        from services.composio_openai_service import ComposioXMLService
        
        # Create service with API key
        service = ComposioXMLService.from_env()
        
        # Test user ID
        test_user_id = "d4b8c8e0-1234-5678-9abc-def012345678"
        
        print(f"   👤 Testing service discovery for user: {test_user_id}")
        
        # Get user's active services
        active_services = await service.get_user_active_services(test_user_id)
        
        print(f"   📊 Active services found: {len(active_services)}")
        for service_name in active_services:
            print(f"      🔗 {service_name}")
        
        if "clickup" in active_services:
            print("   ✅ ClickUp is in user's active services!")
            return True
        else:
            print("   ❌ ClickUp not in user's active services")
            print("   ℹ️ User needs to connect ClickUp account via frontend")
            return False
        
    except Exception as e:
        print(f"   ❌ Service discovery test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_tool_registration():
    """Test that ClickUp tools get registered properly."""
    print("\n📝 Testing tool registration...")
    
    try:
        from agentpress.tool_registry import ToolRegistry
        from agent.tools.composio_xml_factory import ComposioXMLToolFactory
        
        # Create registry and factory
        registry = ToolRegistry()
        factory = ComposioXMLToolFactory()
        
        # Test user ID
        test_user_id = "d4b8c8e0-1234-5678-9abc-def012345678"
        
        print(f"   👤 Testing tool registration for user: {test_user_id}")
        
        # Create tools
        tools = await factory.create_user_tools(test_user_id)
        
        # Register tools
        for tool in tools:
            registry.register_tool(tool)
        
        # Check XML tools for ClickUp
        xml_tools = registry.xml_tools
        clickup_xml_tools = {tag: info for tag, info in xml_tools.items() 
                           if 'clickup' in tag.lower()}
        
        print(f"   📊 Total XML tools registered: {len(xml_tools)}")
        print(f"   📋 ClickUp XML tools registered: {len(clickup_xml_tools)}")
        
        if clickup_xml_tools:
            print("   ✅ ClickUp XML tools are properly registered!")
            for tag in list(clickup_xml_tools.keys())[:3]:  # Show first 3
                print(f"      🏷️ {tag}")
            if len(clickup_xml_tools) > 3:
                print(f"      ... and {len(clickup_xml_tools) - 3} more")
            return True
        else:
            print("   ❌ No ClickUp XML tools registered")
            return False
        
    except Exception as e:
        print(f"   ❌ Tool registration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all agent visibility tests."""
    print("🚀 Agent ClickUp Tool Visibility Test")
    print("=" * 60)
    
    test_results = {
        "tool_loading": await test_agent_tool_loading(),
        "service_discovery": await test_service_discovery(),
        "tool_registration": await test_tool_registration(),
    }
    
    print("\n" + "=" * 60)
    print("📊 AGENT VISIBILITY TEST RESULTS")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! Agent can see and use ClickUp tools.")
        print("\n💡 ClickUp is fully integrated and ready for agent use.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("\n💡 Common issues:")
        print("1. User needs to connect ClickUp account via frontend")
        print("2. Check COMPOSIO_API_KEY environment variable")
        print("3. Verify user has active ClickUp connection in database")
    
    return all_passed

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
